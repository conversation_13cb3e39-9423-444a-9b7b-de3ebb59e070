# 全局搜索功能实现说明

## 概述
本次实现将 `searchQuery` 定义为全局变量，使其能够被外部页面使用和访问。

## 实现架构

### 1. 全局搜索状态管理器 (`GlobalSearchManager`)
- **位置**: `app/src/main/java/com/crrc/siom/ui/workorder/GlobalSearchManager.kt`
- **功能**: 
  - 管理全局搜索状态
  - 提供搜索查询的更新和清空方法
  - 使用 StateFlow 实现响应式状态管理

```kotlin
object GlobalSearchManager {
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    fun updateSearchQuery(query: String)
    fun clearSearchQuery()
    fun getCurrentSearchQuery(): String
}
```

### 2. WorkOrderViewModel 增强
- **新增功能**:
  - 本地搜索状态管理
  - 与全局搜索状态同步
  - 搜索查询更新和清空方法

### 3. BaseWorkOrderViewModelImpl 增强
- **新增功能**:
  - 搜索查询状态管理
  - `setSearchQuery()` 方法
  - `getCurrentFilterParams()` 方法（包含搜索参数）

### 4. 工单页面修改
所有工单页面都已修改以支持搜索参数：
- `PlannedWorkOrderPage`
- `FaultWorkOrderPage` 
- `EmergencyWorkOrderPage`
- `TemporaryWorkOrderPage`

每个页面都：
- 接收 `searchQuery` 参数
- 使用 `LaunchedEffect` 监听搜索查询变化
- 自动调用 ViewModel 的 `setSearchQuery()` 方法

### 5. WorkOrderActivity 修改
- 使用全局搜索状态
- 将搜索查询传递给子页面
- 搜索输入时同步更新全局状态

## 使用方式

### 在外部页面中访问全局搜索状态

```kotlin
@Composable
fun ExternalPage() {
    // 监听全局搜索状态
    val globalSearchQuery by GlobalSearchManager.searchQuery.collectAsState()
    
    // 响应搜索查询变化
    LaunchedEffect(globalSearchQuery) {
        if (globalSearchQuery.isNotBlank()) {
            // 执行搜索相关操作
        }
    }
    
    // 更新全局搜索状态
    Button(
        onClick = {
            GlobalSearchManager.updateSearchQuery("新的搜索内容")
        }
    ) {
        Text("设置搜索")
    }
}
```

### 在外部页面中修改搜索状态

```kotlin
// 更新搜索查询
GlobalSearchManager.updateSearchQuery("搜索内容")

// 清空搜索查询
GlobalSearchManager.clearSearchQuery()

// 获取当前搜索查询
val currentQuery = GlobalSearchManager.getCurrentSearchQuery()
```

## 数据流

1. **用户输入** → `WorkOrderActivity` 的 `SearchField`
2. **搜索输入** → `WorkOrderViewModel.updateSearchQuery()`
3. **ViewModel** → `GlobalSearchManager.updateSearchQuery()`
4. **全局状态更新** → 所有监听的页面收到通知
5. **子页面** → 通过 `LaunchedEffect` 监听变化
6. **子页面 ViewModel** → 调用 `setSearchQuery()` 更新本地状态
7. **数据加载** → 使用 `getCurrentFilterParams()` 包含搜索参数

## 优势

1. **全局一致性**: 所有页面共享同一个搜索状态
2. **响应式更新**: 使用 StateFlow 实现自动更新
3. **解耦设计**: 外部页面可以独立访问和修改搜索状态
4. **向后兼容**: 不影响现有的过滤功能
5. **易于扩展**: 可以轻松添加更多全局状态

## 注意事项

1. **搜索参数传递**: 搜索查询通过 `getCurrentFilterParams()` 方法包含在过滤参数中
2. **状态同步**: 本地和全局搜索状态保持同步
3. **生命周期**: GlobalSearchManager 是单例，在应用生命周期内保持状态
4. **内存管理**: StateFlow 会自动管理订阅者的生命周期

## 示例代码

参考 `GlobalSearchUsageExample.kt` 文件查看完整的使用示例。
