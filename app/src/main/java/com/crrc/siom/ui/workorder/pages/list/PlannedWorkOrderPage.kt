package com.crrc.siom.ui.workorder.pages.list

import android.app.Activity
import android.content.Intent
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.siom.ui.workorder.components.FilterBar
import com.crrc.common.bean.response.PlanOrderListResponse.PlanOrderRecord
import com.crrc.common.bean.response.MembersResponse
import com.crrc.siom.ui.workorder.components.BaseWorkOrderPage
import com.crrc.siom.ui.workorder.viewmodel.list.PlannedWorkOrderViewModel
import com.crrc.siom.ui.workorder.components.WorkOrderCard
import com.crrc.siom.ui.workorder.PickMaterialActivity
import com.crrc.siom.ui.workorder.WorkOrderDetailActivity
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import com.crrc.siom.ui.workorder.viewmodel.FilterViewModel
import com.crrc.siom.ui.components.MultiSelectDialog
import com.crrc.siom.ui.components.SingleSelectDialog
import com.crrc.siom.ui.workorder.PickMaterialActivity.Companion.EXTRA_WORK_ORDER_ID
import com.crrc.siom.ui.workorder.ProcessWorkOrderActivity
import com.crrc.siom.ui.workorder.components.FilterDropdown
import com.crrc.siom.ui.workorder.components.LocalAcceptDialogState
import com.crrc.siom.ui.workorder.components.LocalArriveDialogState
import com.crrc.siom.ui.workorder.components.LocalCancelDialogState
import com.crrc.siom.ui.workorder.components.LocalConfirmDialogState
import com.crrc.siom.ui.workorder.components.LocalDealDialogState
import com.crrc.siom.ui.workorder.components.LocalRollbackDialogState

@Composable
fun PlannedWorkOrderPage(
    viewModel: PlannedWorkOrderViewModel = viewModel(),
    filterViewModel: FilterViewModel = viewModel(key = "planned_filter")
) {
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            viewModel.refresh()
        }
    }
    // 指派相关状态
    var showAssignDialog by remember { mutableStateOf(false) }
    var membersResponse by remember { mutableStateOf<MembersResponse?>(null) }
    var selectedWorkOrderId by remember { mutableStateOf("") }
    var selectedMember by remember { mutableStateOf<MembersResponse.FilterParam?>(null) }

    val context = LocalContext.current

    // 处理指派
    fun handleAssign(workOrderId: String) {
        viewModel.assignOpt(workOrderId) { response, error ->
            if (response != null) {
                membersResponse = response
                showAssignDialog = true
            } else {
                Toast.makeText(context, "获取可指派人员失败: $error", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // 提交指派
    fun submitAssignment(workOrderId: String) {
        val allMembers = membersResponse?.members?.map { member ->
            val modifiedMember = MembersResponse.FilterParam()
            modifiedMember.id = member.id
            modifiedMember.name = member.name
            modifiedMember.isSelected = (member.id == selectedMember?.id)
            modifiedMember
        }
        viewModel.assign(workOrderId, allMembers ?: emptyList()) { success, msg ->
            if (success) {
                Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
                viewModel.refresh()
            } else {
                Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
            }
            showAssignDialog = false
        }
    }

    // 指派对话框
    if (showAssignDialog && membersResponse != null) {
        SingleSelectDialog(
            title = "选择指派人员",
            items = membersResponse?.members ?: emptyList(),
            selectedItem = selectedMember,
            itemText = { member -> member.name },
            onDismiss = { showAssignDialog = false },
            onConfirm = { selected ->
                selectedMember = selected
                submitAssignment(selectedWorkOrderId)
            }
        )
    }

    // 使用简化版的BaseWorkOrderPage
    BaseWorkOrderPage(
        viewModel = viewModel,
        filterBar = {
            FilterDropdown(
                onFilterChanged = { filterParams ->
                    viewModel.setFilterParams(filterParams)
                },
                filterViewModel = filterViewModel,
                isPlanOrder = true
            )
        },
        itemContent = { workOrder ->
            val rollbackDialogState = LocalRollbackDialogState.current
            val cancelDialogState = LocalCancelDialogState.current
            val acceptDialogState = LocalAcceptDialogState.current
            val dealDialogState = LocalDealDialogState.current
            val confirmDialogState = LocalConfirmDialogState.current
            val arriveDialogState = LocalArriveDialogState.current

            PlannedWorkOrderCard(
                workOrder = workOrder,
                onRollback = { id -> rollbackDialogState.show(id) },
                onCancel = { id -> cancelDialogState.show(id) },
                onMaterial = {
                    val intent = Intent(context, PickMaterialActivity::class.java).apply {
                        putExtra(EXTRA_WORK_ORDER_ID, workOrder.id)
                    }
                    launcher.launch(intent)
                },
                onAccept = { id -> acceptDialogState.show(id) },
                onProcess = { id ->
                    if (workOrder.status == "待处理") {
                        viewModel.dealWorkOrder(id)
                    }
                    val intent = Intent(context, ProcessWorkOrderActivity::class.java).apply {
                        putExtra(EXTRA_WORK_ORDER_ID, workOrder.id)
                    }
                    launcher.launch(intent)
                },
                onComplete = { id -> confirmDialogState.show(id) },
                onArrive = { id -> arriveDialogState.show(id) },
                onAssign = { id ->
                    selectedWorkOrderId = id
                    handleAssign(id)
                }
            )
        }
    )
}

@Composable
private fun PlannedWorkOrderCard(
    workOrder: PlanOrderRecord,
    modifier: Modifier = Modifier,
    onRollback: (String) -> Unit,
    onCancel: (String) -> Unit,
    onMaterial: (String) -> Unit,
    onAccept: (String) -> Unit,
    onProcess: (String) -> Unit,
    onComplete: (String) -> Unit,
    onArrive: (String) -> Unit,
    onAssign: (String) -> Unit
) {
    val context = LocalContext.current

    WorkOrderCard(
        workOrder = workOrder,
        onClick = {
            WorkOrderDetailActivity.start(context, workOrder.id)
        },
        onAccept = { onAccept(workOrder.id) },
        onAssign = { onAssign(workOrder.id) },
        onReturn = { onRollback(workOrder.id) },
        onVoid = { onCancel(workOrder.id) },
        onMaterial = { onMaterial(workOrder.id) },
        onArrive = { onArrive(workOrder.id) },
        onProcess = { onProcess(workOrder.id) },
        onConfirm = { onComplete(workOrder.id) },
        modifier = modifier
    )
}