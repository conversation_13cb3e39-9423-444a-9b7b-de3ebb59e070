package com.crrc.siom.ui.workorder

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * 全局搜索使用示例
 * 展示如何在外部页面使用全局搜索状态
 */
@Composable
fun GlobalSearchUsageExample() {
    // 监听全局搜索状态
    val globalSearchQuery by GlobalSearchManager.searchQuery.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "全局搜索状态示例",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "当前全局搜索查询: \"$globalSearchQuery\"",
            style = MaterialTheme.typography.bodyLarge
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 外部页面可以直接更新全局搜索状态
        Button(
            onClick = {
                GlobalSearchManager.updateSearchQuery("外部页面设置的搜索")
            }
        ) {
            Text("从外部页面设置搜索")
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Button(
            onClick = {
                GlobalSearchManager.clearSearchQuery()
            }
        ) {
            Text("清空搜索")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "使用说明:",
            style = MaterialTheme.typography.titleMedium
        )
        
        Text(
            text = """
            1. 在 WorkOrderActivity 中输入搜索内容
            2. 搜索状态会自动同步到所有工单页面
            3. 外部页面可以通过 GlobalSearchManager 访问和修改搜索状态
            4. 所有页面都会实时响应搜索状态的变化
            """.trimIndent(),
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

/**
 * 在其他页面中使用全局搜索的示例
 */
@Composable
fun ExternalPageWithGlobalSearch() {
    val globalSearchQuery by GlobalSearchManager.searchQuery.collectAsState()
    
    // 当搜索查询变化时执行某些操作
    LaunchedEffect(globalSearchQuery) {
        if (globalSearchQuery.isNotBlank()) {
            // 执行搜索相关的操作
            println("外部页面检测到搜索查询变化: $globalSearchQuery")
        }
    }
    
    Column {
        Text("外部页面")
        Text("当前搜索: $globalSearchQuery")
        
        // 可以在外部页面修改搜索状态
        Button(
            onClick = {
                GlobalSearchManager.updateSearchQuery("来自外部页面的搜索")
            }
        ) {
            Text("更新搜索")
        }
    }
}
