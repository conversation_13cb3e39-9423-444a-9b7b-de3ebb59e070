package com.crrc.siom.ui.workorder

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 全局搜索状态管理器
 * 用于在工单页面之间共享搜索查询状态
 */
object GlobalSearchManager {
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    /**
     * 更新搜索查询
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * 清空搜索查询
     */
    fun clearSearchQuery() {
        _searchQuery.value = ""
    }
    
    /**
     * 获取当前搜索查询
     */
    fun getCurrentSearchQuery(): String {
        return _searchQuery.value
    }
}
